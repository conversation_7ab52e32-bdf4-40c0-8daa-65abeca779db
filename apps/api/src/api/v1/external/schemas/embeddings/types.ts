/**
 * TypeScript type definitions for embeddings API
 * Only contains error codes enum - other types are defined in individual schema files
 */

// Error codes for embeddings operations
export enum EmbeddingsErrorCode {
  MISSING_ORGANIZATION_ID = 'MISSING_ORGA<PERSON>ZATION_ID',
  INVA<PERSON>ID_OR<PERSON>NIZATION_ID = 'INVA<PERSON><PERSON>_ORGANIZATION_ID',
  EMBEDDINGS_RETRIEVAL_FAILED = 'EMBEDDINGS_RETRIEVAL_FAILED',
  EMBEDDINGS_UPDATE_FAILED = 'EMBEDDINGS_UPDATE_FAILED',
  EMBEDDINGS_SUBMIT_FAILED = 'EMBEDDINGS_SUBMIT_FAILED',
  DOCUMENT_RETRIEVAL_FAILED = 'DOCUMENT_RETRIEVAL_FAILED',
  DOCUMENT_PARSING_FAILED = 'DOCUMENT_PARSING_FAILED',
  INDEXING_FAILED = 'INDEXING_FAILED',
  INVALID_REQUEST_BODY = 'INVALID_REQUEST_BODY',
  INVALID_DOCUMENT = 'INVALID_DOCUMENT',
  INSUFFICIENT_CONTENT = 'INSUFFICIENT_CONTENT',
}
