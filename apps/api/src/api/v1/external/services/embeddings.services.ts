import { FastifyInstance } from 'fastify';
import {
  OrganizationDataViewer,
  KeyMetadata,
} from '../../../../core/embeddings/organization-data-viewer';
import { MCPConnectionManager } from '@anter/agents';
import {
  GetEmbeddingsResponse,
  UpdateEmbeddingsResponse,
  SubmitEmbeddingsResponse,
  SubmitEmbeddingsRequest,
  DocumentChunk,
  EmbeddingsErrorCode,
} from '../schemas/embeddings';

/**
 * Custom error classes for embeddings operations
 */
export class EmbeddingsError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: string
  ) {
    super(message);
    this.name = 'EmbeddingsError';
  }
}

export class OrganizationIdMissingError extends EmbeddingsError {
  constructor() {
    super(
      'X-Organization-Id header is required and must be a string',
      EmbeddingsErrorCode.MISSING_ORGANIZATION_ID,
      400
    );
  }
}

export class InvalidOrganizationIdError extends EmbeddingsError {
  constructor() {
    super(
      'Invalid organization ID format. Expected format: org-{uuid}',
      EmbeddingsErrorCode.INVALID_ORGANIZATION_ID,
      400
    );
  }
}

export class EmbeddingsRetrievalError extends EmbeddingsError {
  constructor(details?: string) {
    super(
      'Failed to retrieve embeddings data',
      EmbeddingsErrorCode.EMBEDDINGS_RETRIEVAL_FAILED,
      500,
      details
    );
  }
}

export class EmbeddingsUpdateError extends EmbeddingsError {
  constructor(details?: string) {
    super(
      'Failed to update embeddings',
      EmbeddingsErrorCode.EMBEDDINGS_UPDATE_FAILED,
      500,
      details
    );
  }
}

export class EmbeddingsSubmitError extends EmbeddingsError {
  constructor(details?: string) {
    super(
      'Failed to submit embeddings',
      EmbeddingsErrorCode.EMBEDDINGS_SUBMIT_FAILED,
      500,
      details
    );
  }
}

export class DocumentRetrievalError extends EmbeddingsError {
  constructor(details?: string) {
    super(
      'Failed to retrieve documents from database',
      EmbeddingsErrorCode.DOCUMENT_RETRIEVAL_FAILED,
      500,
      details
    );
  }
}

export class DocumentParsingError extends EmbeddingsError {
  constructor(details?: string) {
    super(
      'Failed to parse documents from MCP tool',
      EmbeddingsErrorCode.DOCUMENT_PARSING_FAILED,
      500,
      details
    );
  }
}

export class IndexingError extends EmbeddingsError {
  constructor(details?: string) {
    super(
      'Failed to index documents for semantic search',
      EmbeddingsErrorCode.INDEXING_FAILED,
      500,
      details
    );
  }
}

export class InvalidRequestBodyError extends EmbeddingsError {
  constructor(message: string = 'Request body must contain a documents array') {
    super(message, EmbeddingsErrorCode.INVALID_REQUEST_BODY, 400);
  }
}

export class InvalidDocumentError extends EmbeddingsError {
  constructor(message: string = 'Each document must have id, name, and content fields') {
    super(message, EmbeddingsErrorCode.INVALID_DOCUMENT, 400);
  }
}

export class InsufficientContentError extends EmbeddingsError {
  constructor(documentId: string) {
    super(
      `Document ${documentId} has insufficient content (minimum 20 characters)`,
      EmbeddingsErrorCode.INSUFFICIENT_CONTENT,
      400
    );
  }
}

/**
 * Service class for handling embeddings operations
 */
export class EmbeddingsService {
  constructor(private fastify: FastifyInstance) {}

  /**
   * Validate organization ID header
   */
  private validateOrganizationId(organizationIdHeader: unknown): string {
    if (!organizationIdHeader || typeof organizationIdHeader !== 'string') {
      throw new OrganizationIdMissingError();
    }
    return organizationIdHeader;
  }

  /**
   * Validate organization ID format (when required)
   */
  private validateOrganizationIdFormat(organizationId: string): void {
    if (!organizationId.match(/^org-[a-f0-9-]+$/)) {
      throw new InvalidOrganizationIdError();
    }
  }

  /**
   * Create MCP connection manager adapter
   */
  private createMCPConnectionManagerAdapter() {
    return {
      dbWithTenant: (orgId: string) => this.fastify.dbWithTenant(orgId),
      dbInternalWithTenant: (orgId: string) => this.fastify.dbInternalWithTenant(orgId),
      dbBypassRLS: () => this.fastify.dbBypassRLS(),
      dbInternalBypassRLS: () => this.fastify.dbInternalBypassRLS(),
      getDatabaseConfig: (schema: 'internal' | 'external') => {
        return this.fastify.getDatabaseConfig(schema);
      },
      log: {
        debug: (message: string) => this.fastify.log.debug(message),
        info: (message: string) => this.fastify.log.info(message),
        warn: (message: string) => this.fastify.log.warn(message),
        error: (error: any, message: string) => this.fastify.log.error(error, message),
      },
      redis: this.fastify.redis,
    };
  }

  /**
   * Get embeddings data for a specific organization
   */
  async getEmbeddings(
    organizationIdHeader: unknown,
    requestId: string
  ): Promise<GetEmbeddingsResponse> {
    try {
      const organizationId = this.validateOrganizationId(organizationIdHeader);

      this.fastify.log.info('Getting embeddings data', { organizationId, requestId });

      // Create viewer instance
      const viewer = new OrganizationDataViewer(organizationId, this.fastify.redis);

      // Get all keys for the organization
      const keys = await viewer.getAllKeys();
      const totalKeys = keys.contentHashes.length + keys.docCache.length + keys.embeddings.length;

      // Get detailed metadata for each key
      const contentHashes = await Promise.all(
        keys.contentHashes.map((key: string) => viewer.getKeyMetadata(key))
      );

      const docCache = await Promise.all(
        keys.docCache.map((key: string) => viewer.getKeyMetadata(key))
      );

      const embeddings = await Promise.all(
        keys.embeddings.map((key: string) => viewer.getKeyMetadata(key))
      );

      // Calculate totals
      const totalSize = [...contentHashes, ...docCache, ...embeddings].reduce(
        (sum, key) => sum + key.size,
        0
      );

      // Group embeddings by document for better organization
      const documentGroups: Record<string, KeyMetadata[]> = {};
      embeddings.forEach((metadata: KeyMetadata) => {
        const docMatch = metadata.key.match(/embeddings:[^:]+:([^:]+)::\d+$/);
        const docId = docMatch ? docMatch[1] : 'unknown';

        if (!documentGroups[docId]) {
          documentGroups[docId] = [];
        }
        documentGroups[docId].push(metadata);
      });

      // Format document groups
      const formattedDocumentGroups: DocumentChunk[] = Object.entries(documentGroups).map(
        ([docId, metadatas]) => {
          const totalDocSize = metadatas.reduce((sum, m) => sum + m.size, 0);
          const chunkCount = metadatas.length;

          return {
            documentId: docId,
            chunkCount,
            totalSize: totalDocSize,
            sizeFormatted: viewer.formatBytes(totalDocSize),
            chunks: metadatas,
          };
        }
      );

      const response: GetEmbeddingsResponse = {
        organizationId,
        summary: {
          contentHashes: keys.contentHashes.length,
          docCache: keys.docCache.length,
          embeddings: keys.embeddings.length,
          totalKeys,
          totalSize,
          totalSizeFormatted: viewer.formatBytes(totalSize),
        },
        data: {
          contentHashes,
          docCache,
          embeddings: formattedDocumentGroups,
        },
      };

      this.fastify.log.info('Successfully retrieved embeddings data', {
        organizationId,
        requestId,
        totalKeys,
        totalSize,
      });

      return response;
    } catch (error) {
      this.fastify.log.error('Error retrieving embeddings data', { error, requestId });
      if (error instanceof EmbeddingsError) {
        throw error;
      }
      throw new EmbeddingsRetrievalError(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Clear existing embeddings for an organization
   */
  private async clearExistingEmbeddings(organizationId: string, redisClient: any): Promise<void> {
    try {
      // Clear all embeddings for this organization
      const pattern = `embeddings:${organizationId}:*`;
      const keys = await redisClient.keys(pattern);
      if (keys.length > 0) {
        await redisClient.del(...keys);
        this.fastify.log.info('Cleared existing embeddings', {
          organizationId,
          clearedKeys: keys.length,
        });
      }

      // Clear document cache for this organization
      const cachePattern = `doc_cache:${organizationId}`;
      await redisClient.del(cachePattern);
      this.fastify.log.info('Cleared document cache', { organizationId });

      // Clear content hashes for this organization
      const hashPattern = `content_hashes:${organizationId}`;
      await redisClient.del(hashPattern);
      this.fastify.log.info('Cleared content hashes', { organizationId });
    } catch (redisError) {
      this.fastify.log.warn('Failed to clear existing embeddings', {
        organizationId,
        error: redisError,
      });
      // Continue with the process even if clearing fails
    }
  }

  /**
   * Retrieve documents using MCP tool
   */
  private async retrieveDocuments(
    mcpConnectionManager: MCPConnectionManager,
    organizationId: string
  ): Promise<any[]> {
    this.fastify.log.info('Retrieving documents from database', { organizationId });

    const documentsResult = await mcpConnectionManager.executeToolCall(
      organizationId,
      'get_all_internal_documents',
      {
        limit: 1000,
        offset: 0,
        filter: {
          status: 'active', // Only active documents
        },
        includeIdsOnly: false, // We need full document data
      }
    );

    if (!documentsResult.success || !documentsResult.data) {
      this.fastify.log.error('Failed to retrieve documents', {
        organizationId,
        error: documentsResult.error,
      });
      throw new DocumentRetrievalError(documentsResult.error?.toString());
    }

    // Parse the documents from the MCP tool result
    let documents: any[] = [];
    try {
      // Debug: Log the raw response structure
      this.fastify.log.info('MCP tool response structure', {
        organizationId,
        hasData: !!documentsResult.data,
        dataType: typeof documentsResult.data,
        dataKeys: documentsResult.data ? Object.keys(documentsResult.data) : [],
        rawData: JSON.stringify(documentsResult.data, null, 2),
      });

      // The MCP bridge already parses the JSON response, so we can use it directly
      const parsedResult = documentsResult.data;
      if (!parsedResult || typeof parsedResult !== 'object') {
        throw new Error('MCP tool returned empty or invalid response');
      }

      // Comprehensive validation of parsed result
      if (!parsedResult || typeof parsedResult !== 'object') {
        throw new Error('Invalid JSON structure returned from MCP tool');
      }

      // Validate success field
      if (typeof parsedResult.success !== 'boolean') {
        throw new Error('Invalid success field in MCP tool response');
      }

      if (parsedResult.success) {
        // Validate documents array
        if (!Array.isArray(parsedResult.documents)) {
          throw new Error('Invalid documents field in MCP tool response');
        }

        // Validate each document has required fields
        for (const doc of parsedResult.documents) {
          if (!doc || typeof doc !== 'object') {
            throw new Error('Invalid document structure in MCP tool response');
          }
          if (!doc.id || typeof doc.id !== 'string') {
            throw new Error('Invalid document ID in MCP tool response');
          }
        }

        documents = parsedResult.documents;
      } else {
        throw new Error(`MCP tool returned error: ${parsedResult.error || 'Unknown error'}`);
      }
    } catch (parseError) {
      this.fastify.log.error('Failed to parse documents from MCP tool', {
        organizationId,
        error: parseError,
      });
      throw new DocumentParsingError(
        parseError instanceof Error ? parseError.message : 'Unknown parsing error'
      );
    }

    this.fastify.log.info('Retrieved documents from database', {
      organizationId,
      documentCount: documents.length,
    });

    return documents;
  }

  /**
   * Update embeddings for a specific organization
   */
  async updateEmbeddings(
    organizationIdHeader: unknown,
    requestId: string
  ): Promise<UpdateEmbeddingsResponse> {
    try {
      const organizationId = this.validateOrganizationId(organizationIdHeader);

      // Validate organization ID format for update operations
      this.validateOrganizationIdFormat(organizationId);

      this.fastify.log.info('Starting embeddings update process', { organizationId, requestId });

      // Create MCP connection manager
      const connectionManagerAdapter = this.createMCPConnectionManagerAdapter();
      const mcpConnectionManager = new MCPConnectionManager(connectionManagerAdapter);

      // Step 1: Retrieve all documents using MCP tool
      const documents = await this.retrieveDocuments(mcpConnectionManager, organizationId);

      // Step 2: Clear existing embeddings for this organization
      this.fastify.log.info('Clearing existing embeddings', { organizationId });
      const redisClient = (mcpConnectionManager as any).getRedisClient?.();
      if (redisClient) {
        await this.clearExistingEmbeddings(organizationId, redisClient);
      }

      // Step 3: Process and index documents
      this.fastify.log.info('Processing and indexing documents', {
        organizationId,
        documentCount: documents.length,
      });

      // Import required services for document processing
      const {
        DocumentProcessor,
        DocumentSanitizerService,
        SemanticSearchServiceEnhanced,
        EmbeddingService,
        ContentHashService,
      } = require('@anter/mcp-tools');

      // Create services
      const sanitizer = new DocumentSanitizerService();
      const processor = new DocumentProcessor(sanitizer);
      const embeddingService = new EmbeddingService({ organizationId });
      const contentHashService = new ContentHashService();
      const semanticSearchService = new SemanticSearchServiceEnhanced(
        embeddingService,
        contentHashService,
        this.fastify.log,
        redisClient
      );

      // Process documents
      const processedDocuments = [];
      for (const doc of documents) {
        try {
          this.fastify.log.info('Processing document', {
            organizationId,
            documentId: doc.id,
            hasContent: !!doc.content,
            hasBufferFile: !!doc.buffer_file,
            fileType: doc.fileType || doc.file_type,
          });

          // Use the correct method for processing documents with buffer_file
          const processedChunks = await processor.processDocumentContent({
            id: doc.id,
            name: doc.name,
            content: doc.content,
            buffer_file: doc.buffer_file,
            fileType: doc.fileType || doc.file_type,
            file_type: doc.file_type,
            organization_id: doc.organization_id || organizationId,
            status: doc.status,
            created_at: doc.createdAt || doc.created_at,
            updated_at: doc.updatedAt || doc.updated_at,
          });

          // Filter out null chunks and add to processed documents
          const validChunks = processedChunks.filter((chunk: any) => chunk !== null);
          processedDocuments.push(...validChunks);

          this.fastify.log.info('Document processed successfully', {
            organizationId,
            documentId: doc.id,
            chunksProcessed: validChunks.length,
            totalChunks: processedChunks.length,
          });
        } catch (processError) {
          this.fastify.log.warn('Failed to process document', {
            organizationId,
            documentId: doc.id,
            error: processError,
          });
          // Continue with other documents
        }
      }

      this.fastify.log.info('Processed documents', {
        organizationId,
        processedCount: processedDocuments.length,
      });

      // Step 4: Index documents for semantic search
      if (processedDocuments.length > 0) {
        this.fastify.log.info('Indexing documents for semantic search', {
          organizationId,
          documentCount: processedDocuments.length,
        });

        try {
          await semanticSearchService.indexDocumentsIncremental(
            processedDocuments,
            organizationId,
            {
              redisClient,
              logger: this.fastify.log,
              maxTokens: 1500,
              overlapTokens: 150,
            }
          );

          this.fastify.log.info('Successfully indexed documents for semantic search', {
            organizationId,
            indexedCount: processedDocuments.length,
          });
        } catch (indexError) {
          this.fastify.log.error('Failed to index documents for semantic search', {
            organizationId,
            error: indexError,
          });
          throw new IndexingError(
            indexError instanceof Error ? indexError.message : 'Unknown indexing error'
          );
        }
      }

      // Step 5: Return success response
      const response: UpdateEmbeddingsResponse = {
        success: true,
        organizationId,
        summary: {
          documentsRetrieved: documents.length,
          documentsProcessed: processedDocuments.length,
          documentsIndexed: processedDocuments.length,
          timestamp: new Date().toISOString(),
        },
        message: 'Embeddings updated successfully',
      };

      this.fastify.log.info('Embeddings update completed successfully', {
        organizationId,
        requestId,
        summary: response.summary,
      });

      return response;
    } catch (error) {
      this.fastify.log.error('Error updating embeddings', { error, requestId });
      if (error instanceof EmbeddingsError) {
        throw error;
      }
      throw new EmbeddingsUpdateError(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Validate submitted documents
   */
  private validateSubmittedDocuments(documents: any[]): void {
    for (const doc of documents) {
      if (!doc.id || !doc.name || !doc.content) {
        throw new InvalidDocumentError();
      }
      if (doc.content.trim().length < 20) {
        throw new InsufficientContentError(doc.id);
      }
    }
  }

  /**
   * Submit text documents for embedding generation
   */
  async submitEmbeddings(
    organizationIdHeader: unknown,
    request: SubmitEmbeddingsRequest,
    requestId: string
  ): Promise<SubmitEmbeddingsResponse> {
    try {
      const organizationId = this.validateOrganizationId(organizationIdHeader);

      // Parse and validate request body
      if (!request || !request.documents || !Array.isArray(request.documents)) {
        throw new InvalidRequestBodyError();
      }

      const { documents, options = {} } = request;
      const { maxTokens = 1500, overlapTokens = 150, clearExisting = false } = options;

      this.fastify.log.info('Starting embeddings submit process', {
        organizationId,
        requestId,
        documentCount: documents.length,
        options: { maxTokens, overlapTokens, clearExisting },
      });

      // Validate documents
      this.validateSubmittedDocuments(documents);

      // Create MCP connection manager
      const connectionManagerAdapter = this.createMCPConnectionManagerAdapter();
      const mcpConnectionManager = new MCPConnectionManager(connectionManagerAdapter);

      // Clear existing embeddings if requested
      if (clearExisting) {
        this.fastify.log.info('Clearing existing embeddings', { organizationId });
        const redisClient = (mcpConnectionManager as any).getRedisClient?.();
        if (redisClient) {
          await this.clearExistingEmbeddings(organizationId, redisClient);
        }
      }

      this.fastify.log.info('Processing and indexing submitted documents', {
        organizationId,
        documentCount: documents.length,
      });

      // Import required services for document processing
      const {
        SemanticSearchServiceEnhanced,
        EmbeddingService,
        ContentHashService,
      } = require('@anter/mcp-tools');

      // Create services
      const embeddingService = new EmbeddingService({ organizationId });
      const contentHashService = new ContentHashService();
      const redisClient = (mcpConnectionManager as any).getRedisClient?.();
      const semanticSearchService = new SemanticSearchServiceEnhanced(
        embeddingService,
        contentHashService,
        this.fastify.log,
        redisClient
      );

      // Convert submitted documents to ProcessedDocument format
      const processedDocuments = [];
      for (const doc of documents) {
        try {
          this.fastify.log.info('Processing submitted document', {
            organizationId,
            documentId: doc.id,
            contentLength: doc.content.length,
          });

          // Create a ProcessedDocument directly from the submitted content
          const processedDoc = {
            id: doc.id,
            name: doc.name,
            content: doc.content.trim(),
            metadata: {
              ...doc.metadata,
              organization_id: organizationId,
              source: 'api_submit',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          };

          processedDocuments.push(processedDoc);

          this.fastify.log.info('Document processed successfully', {
            organizationId,
            documentId: doc.id,
            contentLength: processedDoc.content.length,
          });
        } catch (processError) {
          this.fastify.log.warn('Failed to process document', {
            organizationId,
            documentId: doc.id,
            error: processError,
          });
          // Continue with other documents
        }
      }

      this.fastify.log.info('Processed submitted documents', {
        organizationId,
        processedCount: processedDocuments.length,
      });

      // Index documents for semantic search
      let totalChunks = 0;
      let chunkedDocuments: string[] = [];

      if (processedDocuments.length > 0) {
        this.fastify.log.info('Indexing documents for semantic search', {
          organizationId,
          documentCount: processedDocuments.length,
        });

        try {
          const indexingResult = await semanticSearchService.indexDocumentsIncremental(
            processedDocuments,
            organizationId,
            {
              redisClient,
              logger: this.fastify.log,
              maxTokens,
              overlapTokens,
            }
          );

          totalChunks = indexingResult.totalChunks;
          chunkedDocuments = indexingResult.chunkedDocuments;

          this.fastify.log.info('Successfully indexed documents for semantic search', {
            organizationId,
            indexedCount: indexingResult.indexed,
            skippedCount: indexingResult.skipped,
            totalChunks,
            chunkedDocuments,
          });
        } catch (indexError) {
          this.fastify.log.error('Failed to index documents for semantic search', {
            organizationId,
            error: indexError,
          });
          throw new IndexingError(
            indexError instanceof Error ? indexError.message : 'Unknown indexing error'
          );
        }
      }

      // Return success response
      const response: SubmitEmbeddingsResponse = {
        success: true,
        organizationId,
        summary: {
          documentsReceived: documents.length,
          documentsProcessed: processedDocuments.length,
          documentsIndexed: processedDocuments.length,
          totalChunks,
          chunkedDocuments,
          timestamp: new Date().toISOString(),
        },
        message: 'Documents submitted and embeddings generated successfully',
      };

      this.fastify.log.info('Embeddings submit completed successfully', {
        organizationId,
        requestId,
        summary: response.summary,
      });

      return response;
    } catch (error) {
      this.fastify.log.error('Error submitting embeddings', { error, requestId });
      if (error instanceof EmbeddingsError) {
        throw error;
      }
      throw new EmbeddingsSubmitError(error instanceof Error ? error.message : 'Unknown error');
    }
  }
}
