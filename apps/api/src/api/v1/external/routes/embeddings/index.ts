import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import {
  EmbeddingsService,
  OrganizationIdMissingError,
  InvalidOrganizationIdError,
  EmbeddingsRetrievalError,
  EmbeddingsUpdateError,
  EmbeddingsSubmitError,
  DocumentRetrievalError,
  DocumentParsingError,
  IndexingError,
  InvalidRequestBodyError,
  InvalidDocumentError,
  InsufficientContentError,
} from '../../services/embeddings.services';
import {
  GetEmbeddingsSchema,
  UpdateEmbeddingsSchema,
  SubmitEmbeddingsSchema,
  SubmitEmbeddingsRequest,
} from '../../schemas/embeddings';

/**
 * Embeddings routes for external API
 * These routes provide access to organization embeddings data
 */
export default async function embeddingsRoutes(fastify: FastifyInstance): Promise<void> {
  const embeddingsService = new EmbeddingsService(fastify);

  // Get embeddings data for a specific organization
  fastify.get('/embeddings', {
    schema: GetEmbeddingsSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const organizationIdHeader = request.headers['x-organization-id'];
      try {
        const response = await embeddingsService.getEmbeddings(organizationIdHeader, request.id);
        return response;
      } catch (error: any) {
        if (error instanceof OrganizationIdMissingError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof InvalidOrganizationIdError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof EmbeddingsRetrievalError) {
          reply.code(500).send({
            error: 'Internal Server Error',
            code: error.code,
            message: error.message,
          });
          return;
        }
        // Generic error handling
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'EMBEDDINGS_RETRIEVAL_FAILED',
          message: 'Failed to retrieve embeddings data',
        });
      }
    },
  });

  // Update embeddings for a specific organization
  fastify.post('/embeddings/update', {
    schema: UpdateEmbeddingsSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const organizationIdHeader = request.headers['x-organization-id'];
      try {
        const response = await embeddingsService.updateEmbeddings(organizationIdHeader, request.id);
        return response;
      } catch (error: any) {
        if (error instanceof OrganizationIdMissingError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof InvalidOrganizationIdError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof DocumentRetrievalError) {
          reply.code(500).send({
            error: 'Internal Server Error',
            code: error.code,
            message: error.message,
            details: error.details,
          });
          return;
        }
        if (error instanceof DocumentParsingError) {
          reply.code(500).send({
            error: 'Internal Server Error',
            code: error.code,
            message: error.message,
            details: error.details,
          });
          return;
        }
        if (error instanceof IndexingError) {
          reply.code(500).send({
            error: 'Internal Server Error',
            code: error.code,
            message: error.message,
            details: error.details,
          });
          return;
        }
        if (error instanceof EmbeddingsUpdateError) {
          reply.code(500).send({
            error: 'Internal Server Error',
            code: error.code,
            message: error.message,
            details: error.details,
          });
          return;
        }
        // Generic error handling
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'EMBEDDINGS_UPDATE_FAILED',
          message: 'Failed to update embeddings',
          details: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
  });

  // Submit text documents for embedding generation
  fastify.post('/embeddings/submit', {
    schema: SubmitEmbeddingsSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const organizationIdHeader = request.headers['x-organization-id'];
      const requestBody = request.body as SubmitEmbeddingsRequest;
      try {
        const response = await embeddingsService.submitEmbeddings(
          organizationIdHeader,
          requestBody,
          request.id
        );
        return response;
      } catch (error: any) {
        if (error instanceof OrganizationIdMissingError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof InvalidRequestBodyError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof InvalidDocumentError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof InsufficientContentError) {
          reply.code(400).send({
            error: 'Bad Request',
            code: error.code,
            message: error.message,
          });
          return;
        }
        if (error instanceof IndexingError) {
          reply.code(500).send({
            error: 'Internal Server Error',
            code: error.code,
            message: error.message,
            details: error.details,
          });
          return;
        }
        if (error instanceof EmbeddingsSubmitError) {
          reply.code(500).send({
            error: 'Internal Server Error',
            code: error.code,
            message: error.message,
            details: error.details,
          });
          return;
        }
        // Generic error handling
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'EMBEDDINGS_SUBMIT_FAILED',
          message: 'Failed to submit embeddings',
          details: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
  });
}
