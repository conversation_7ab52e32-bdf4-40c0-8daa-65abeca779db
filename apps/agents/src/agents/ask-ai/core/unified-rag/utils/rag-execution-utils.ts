import { AgentInput } from '../../../../../base/types';
import { UnifiedRAGExecutor } from '../core/unified-rag-executor';
import { UnifiedRAGConfigBuilder } from '../configuration/unified-config';
import { UnifiedRAGContext, PromptConfigParams, ResponseOptions, SingleShotResult, StreamingResult } from '../types/unified-rag-types';
import { ResponseStrategyFactory } from '../response-strategies/response-strategy';
import { OrganizationPromptResolver } from '@anter/shared-services';

/**
 * Utilities for RAG execution operations
 */
export class RAGExecutionUtils {
  /**
   * Execute unified RAG for single-shot responses
   */
  static async executeUnifiedRAG(
    input: AgentInput,
    sessionId: string | null,
    traceId: string,
    logger: any,
    unifiedRAGExecutor: UnifiedRAGExecutor,
    responseMode: 'single-shot' | 'streaming'
  ): Promise<{ ragContext: UnifiedRAGContext; responseResult: SingleShotResult | StreamingResult }> {
    const startTime = Date.now();

    try {
      // Get configuration based on response mode
      const config =
        responseMode === 'streaming'
          ? UnifiedRAGConfigBuilder.forStreaming()
          : UnifiedRAGConfigBuilder.forSingleShot();

      // Execute unified RAG pipeline
      // We get here the context with the search results from the redis embedding index
      const ragContext: UnifiedRAGContext = await unifiedRAGExecutor.execute(
        input,
        sessionId || null,
        config,
        traceId
      );

      // Generate response using appropriate strategy
      // NOTE: organizationId is required so consider to return error if this is not provided
      const organizationId = input.organizationId || 'anter-ai';
      const promptConfigParams: PromptConfigParams = {
        organizationId,
        promptName:
          OrganizationPromptResolver.getInstance().getPromptNameForOrganization(organizationId),
        version: 1,
      };

      const responseStrategy = ResponseStrategyFactory.createStrategy(
        responseMode,
        promptConfigParams
      );
      const responseOptions: ResponseOptions = {
        streaming: false,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        topK: config.topK,
        promptType: config.promptType,
      };

      const responseResult = await responseStrategy.generateResponse(ragContext, responseOptions);

      return { ragContext, responseResult: responseResult as SingleShotResult | StreamingResult };
    } catch (error) {
      const elapsedMs = Date.now() - startTime;
      logger.error(error, 'Unified RAG execution failed', {
        sessionId,
        elapsedMs,
      });
      throw error;
    }
  }

  /**
   * Execute unified RAG for streaming responses
   */
  static async executeStreamingRAG(
    input: AgentInput,
    sessionId: string | null,
    traceId: string,
    logger: any,
    unifiedRAGExecutor: UnifiedRAGExecutor
  ): Promise<{ ragContext: UnifiedRAGContext; responseResult: SingleShotResult | StreamingResult }> {
    const startTime = Date.now();

    try {
      // Get configuration for streaming
      const config = UnifiedRAGConfigBuilder.forStreaming();

      // Execute unified RAG pipeline.
      const ragContext: UnifiedRAGContext = await unifiedRAGExecutor.execute(
        input,
        sessionId,
        config,
        traceId
      );

      // Debug: Check what sources we're getting from ragContext in single-shot
      console.log('DEBUG - streaming completion sources:', {
        sources: ragContext.searchResults.metadata.sources,
        sourcesType: typeof ragContext.searchResults.metadata.sources,
        sourcesLength: ragContext.searchResults.metadata.sources?.length,
        isArray: Array.isArray(ragContext.searchResults.metadata.sources),
        firstSource: ragContext.searchResults.metadata.sources?.[0],
        firstSourceType: typeof ragContext.searchResults.metadata.sources?.[0],
      });

      // Generate streaming response
      // NOTE: organizationId is required so consider to return error if this is not provided
      const organizationId = input.organizationId || 'anter-ai';
      const promptConfigParams: PromptConfigParams = {
        organizationId,
        promptName:
          OrganizationPromptResolver.getInstance().getPromptNameForOrganization(organizationId),
        version: 1,
      };

      const responseStrategy = ResponseStrategyFactory.createStrategy(
        'streaming',
        promptConfigParams
      );
      const responseOptions: ResponseOptions = {
        streaming: true,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        topK: config.topK,
        promptType: config.promptType,
      };

      const responseResult = await responseStrategy.generateResponse(ragContext, responseOptions);

      return { ragContext, responseResult: responseResult as SingleShotResult | StreamingResult };
    } catch (error) {
      const elapsedMs = Date.now() - startTime;
      logger.error(error, 'Unified RAG streaming failed', {
        sessionId,
        elapsedMs,
      });
      throw error;
    }
  }
}
