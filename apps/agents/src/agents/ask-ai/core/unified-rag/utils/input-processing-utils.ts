import { AgentInput } from '../../../../../base/types';
import { getLogger } from '../../../../../observability/global-logger';

/**
 * Utilities for processing agent input
 */
export class InputProcessingUtils {
  /**
   * Generate session ID from input or return null if not provided
   */
  static generateSessionId(input: AgentInput): string | null {
    // Use sessionId from input if provided
    if (input.sessionId) {
      return input.sessionId;
    }

    // Log warning if no sessionId provided
    const logger = getLogger();
    logger.warn('No sessionId provided in AgentInput - conversation memory will not be available', {
      organizationId: input.organizationId,
      userId: input.userId,
      hasMetadata: !!input.metadata,
    });

    return null;
  }

  /**
   * Generate trace ID
   */
  static generateTraceId(): string {
    const crypto = require('crypto');
    const randomPart = crypto.randomBytes(4).toString('hex');
    return `trace_${Date.now()}_${randomPart}`;
  }

  /**
   * Extract user input from AgentInput
   */
  static extractUserInput(input: AgentInput): string {
    if (typeof input.input === 'string') {
      return input.input.trim();
    }

    if (typeof input.input === 'object' && input.input !== null) {
      const obj: any = input.input;
      const possibleFields = ['prompt', 'query', 'message', 'input', 'text', 'question', 'content'];

      for (const field of possibleFields) {
        if (obj[field] && typeof obj[field] === 'string' && obj[field].trim()) {
          return obj[field].trim();
        }
      }

      if (Object.keys(obj).length > 0) {
        return JSON.stringify(obj);
      }
    }

    if (input.input === undefined || input.input === null) {
      throw new Error('Input is undefined or null');
    }

    if (typeof input.input === 'number' || typeof input.input === 'boolean') {
      return String(input.input);
    }

    throw new Error('Invalid input format: user input not found in any expected field');
  }
}
