import { AgentChunk } from '../../../../../base/types';
import { UnifiedRAGContext, SingleShotResult, StreamingResult } from '../types/unified-rag-types';
import { ResponseBuildingUtils } from './response-building-utils';
import { MemoryUtils } from './memory-utils';
import { AgentExecutionContext } from './agent-execution-utils';

/**
 * Utilities for handling streaming responses
 */
export class StreamingResponseUtils {
  /**
   * Process streaming response and yield chunks
   */
  static async *processStreamingResponse(
    responseResult: SingleShotResult | StreamingResult,
    ragContext: UnifiedRAGContext,
    context: AgentExecutionContext,
    responseMode: 'single-shot' | 'streaming',
    memory: any,
    logger: any
  ): AsyncGenerator<AgentChunk> {
    // Handle streaming response
    if ('stream' in responseResult && responseResult.stream) {
      const stream = responseResult.stream as AsyncIterable<string>;
      for await (const chunk of stream) {
        const elapsedMs = Date.now() - context.startTime;
        yield ResponseBuildingUtils.createStreamingChunk(chunk, context.sessionId, elapsedMs, responseMode);
      }
    } else {
      // Fallback to single chunk
      const elapsedMs = Date.now() - context.startTime;
      yield ResponseBuildingUtils.createStreamingChunk(responseResult.content, context.sessionId, elapsedMs, responseMode);
    }

    // Store assistant response
    await MemoryUtils.storeAssistantResponse(
      ragContext,
      responseResult,
      logger,
      memory,
      responseMode
    );

    // Send completion chunk
    const finalElapsedMs = Date.now() - context.startTime;

    // Debug: Check what sources we're getting from ragContext in streaming
    console.log('DEBUG - streaming completion sources:', {
      sources: ragContext.searchResults.metadata.sources,
      sourcesType: typeof ragContext.searchResults.metadata.sources,
      sourcesLength: ragContext.searchResults.metadata.sources?.length,
      isArray: Array.isArray(ragContext.searchResults.metadata.sources),
      firstSource: ragContext.searchResults.metadata.sources?.[0],
      firstSourceType: typeof ragContext.searchResults.metadata.sources?.[0],
    });

    yield ResponseBuildingUtils.createCompletionChunk(context.sessionId, finalElapsedMs, responseMode, {
      tokensUsed: responseResult.metadata?.tokens || 0,
      toolsInvoked: [],
      cost: 0,
      warnings: [],
      sources: ragContext.searchResults.metadata.sources, // array of AgentSource objects
    });
  }
}
