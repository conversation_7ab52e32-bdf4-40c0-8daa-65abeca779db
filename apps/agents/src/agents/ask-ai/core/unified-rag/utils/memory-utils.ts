import { <PERSON><PERSON><PERSON>ory } from '../../../../../base/types';
import { UnifiedRAGContext, SingleShotResult, StreamingResult } from '../types/unified-rag-types';

/**
 * Utilities for memory operations
 */
export class MemoryUtils {
  /**
   * Store assistant response in memory
   */
  static async storeAssistantResponse(
    ragContext: UnifiedRAGContext,
    responseResult: SingleShotResult | StreamingResult,
    logger: any,
    memory: AgentMemory,
    responseMode: 'single-shot' | 'streaming'
  ): Promise<void> {
    try {
      const entry = {
        id: `${ragContext.executionContext.sessionId}_assistant_${Date.now()}`,
        timestamp: new Date(),
        type: 'assistant' as const,
        content: responseResult.content,
        metadata: {
          sessionId: ragContext.executionContext.sessionId,
          organizationId: ragContext.executionContext.organizationId,
          responseMode,
          confidence: responseResult.metadata.confidence,
          tokens: responseResult.metadata.tokens,
        },
      };

      await memory.add(entry);

      logger.info('Assistant response stored', {
        sessionId: ragContext.executionContext.sessionId,
        responseLength: responseResult.content.length,
        responseMode,
      });
    } catch (error) {
      logger.error(error, 'Failed to store assistant response', {
        sessionId: ragContext.executionContext.sessionId,
      });
    }
  }
}
