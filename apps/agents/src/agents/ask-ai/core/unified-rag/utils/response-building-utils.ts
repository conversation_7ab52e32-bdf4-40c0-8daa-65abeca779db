import { <PERSON><PERSON><PERSON><PERSON>, Agent<PERSON>hunk, StandardAgentOutput } from '../../../../../base/types';
import { UnifiedRAGContext, SingleShotResult, StreamingResult } from '../types/unified-rag-types';

/**
 * Utilities for building agent responses and chunks
 */
export class ResponseBuildingUtils {
  /**
   * Build agent result from response
   */
  static buildAgentResult(
    responseResult: SingleShotResult | StreamingResult,
    ragContext: UnifiedRAGContext,
    sessionId: string | null,
    totalTime: number
  ): AgentResult {
    const output: StandardAgentOutput = {
      response: responseResult.content,
      metadata: {
        session_id: sessionId || undefined,
        conversation_entries: ragContext.memory.length,
        semantic_search_results: ragContext.searchResults.metadata.resultsCount,
        semantic_search_average_score: ragContext.searchResults.averageScore,
      },
      metrics: {
        tokensUsed: responseResult.metadata.tokens || 0,
        latencyMs: responseResult.metadata.latency || totalTime,
        toolsInvoked: [],
        cost: 0,
        warnings: [],
      },
    };

    // Debug: Check what sources we're getting from ragContext
    console.log('DEBUG - buildAgentResult sources:', {
      sources: ragContext.searchResults.metadata.sources,
      sourcesType: typeof ragContext.searchResults.metadata.sources,
      sourcesLength: ragContext.searchResults.metadata.sources?.length,
      isArray: Array.isArray(ragContext.searchResults.metadata.sources),
      firstSource: ragContext.searchResults.metadata.sources?.[0],
      firstSourceType: typeof ragContext.searchResults.metadata.sources?.[0],
    });

    return {
      output,
      metadata: {
        tokensUsed: responseResult.metadata.tokens || 0,
        latencyMs: responseResult.metadata.latency || totalTime,
        toolsInvoked: [],
        cost: 0,
        warnings: [],
        sources: ragContext.searchResults.metadata.sources, // array of AgentSource objects
      },
    };
  }

  /**
   * Create streaming chunk
   */
  static createStreamingChunk(
    content: string,
    sessionId: string | null,
    elapsedMs: number,
    responseMode: 'single-shot' | 'streaming'
  ): AgentChunk {
    return {
      type: 'content',
      content,
      metadata: {
        sessionId: sessionId || undefined,
        elapsedMs,
        responseMode,
      },
    };
  }

  /**
   * Create completion chunk to signal end of stream
   */
  static createCompletionChunk(
    sessionId: string | null,
    elapsedMs: number,
    responseMode: 'single-shot' | 'streaming',
    metadata?: Record<string, any>
  ): AgentChunk {
    return {
      type: 'complete',
      content: null,
      metadata: {
        sessionId: sessionId || undefined,
        elapsedMs,
        responseMode,
        completed: true,
        ...metadata,
      },
    };
  }

  /**
   * Create error result
   */
  static createErrorResult(
    error: Error,
    sessionId: string | null,
    elapsedMs: number
  ): AgentResult {
    const output: StandardAgentOutput = {
      response: 'I encountered an error while processing your request. Please try again.',
      metadata: {
        session_id: sessionId || undefined,
        error: error.message,
      },
      metrics: {
        tokensUsed: 0,
        latencyMs: elapsedMs,
        toolsInvoked: [],
        cost: 0,
        warnings: [error.message],
      },
    };

    return {
      output,
      metadata: {
        tokensUsed: 0,
        latencyMs: elapsedMs,
        toolsInvoked: [],
        cost: 0,
        warnings: [error.message],
      },
    };
  }

  /**
   * Create error chunk for streaming
   */
  static createErrorChunk(error: Error, sessionId: string | null, elapsedMs: number): AgentChunk {
    return {
      type: 'error',
      content: 'Error: ' + error.message,
      metadata: {
        sessionId: sessionId || undefined,
        error: error.message,
        elapsedMs,
      },
    };
  }
}
