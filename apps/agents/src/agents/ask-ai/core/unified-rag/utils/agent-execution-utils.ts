import { AgentInput, AgentR<PERSON>ult, AgentChunk } from '../../../../../base/types';
import { getLoggerWithTraceId } from '../../../../../observability/global-logger';
import { getLangfuseClient } from '../../../../../observability/langfuse';
import { InputProcessingUtils } from './input-processing-utils';
import { ResponseBuildingUtils } from './response-building-utils';

/**
 * Execution context for agent operations
 */
export interface AgentExecutionContext {
  sessionId: string | null;
  traceId: string;
  logger: any;
  startTime: number;
  agentSpan: any;
  langfuseClient: any;
}

/**
 * Utilities for agent execution operations
 */
export class AgentExecutionUtils {
  /**
   * Initialize execution context for agent operations
   */
  static initializeExecutionContext(
    input: AgentInput,
    agentName: string,
    agentVersion: string,
    responseMode: 'single-shot' | 'streaming'
  ): AgentExecutionContext {
    const sessionId = InputProcessingUtils.generateSessionId(input);
    const traceId = InputProcessingUtils.generateTraceId();
    const logger = getLoggerWithTraceId(traceId);
    const startTime = Date.now();
    const langfuseClient = getLangfuseClient();

    // Create Langfuse span
    let agentSpan = null;
    if (langfuseClient && traceId) {
      try {
        const spanName = responseMode === 'streaming' ? 'ask_ai_agent_streaming' : 'ask_ai_agent_execution';
        const tags = responseMode === 'streaming' ? ['ask-ai', 'agent', 'streaming'] : ['ask-ai'];
        
        agentSpan = langfuseClient.span({
          traceId,
          name: spanName,
          input: InputProcessingUtils.extractUserInput(input),
          metadata: {
            sessionId,
            organizationId: input.organizationId,
            userId: input.userId,
            responseMode,
            agentName,
            agentVersion,
            tags,
          },
        });
        logger.info(`[AskAIAgent] Langfuse span created | sessionId=${sessionId}`);
      } catch (error) {
        logger.warn('[AskAIAgent] Failed to create Langfuse span');
      }
    }

    return {
      sessionId,
      traceId,
      logger,
      startTime,
      agentSpan,
      langfuseClient,
    };
  }

  /**
   * Validate input requirements
   */
  static validateInput(input: AgentInput): void {
    if (!input.organizationId) {
      throw new Error('organizationId is required for secure context isolation');
    }
  }

  /**
   * Log execution start
   */
  static logExecutionStart(
    context: AgentExecutionContext,
    responseMode: 'single-shot' | 'streaming',
    input: AgentInput
  ): void {
    context.logger.info(`AskAIAgent ${responseMode === 'streaming' ? 'doStream' : 'doInvoke'} started`, {
      sessionId: context.sessionId || 'none',
      responseMode,
      inputLength: InputProcessingUtils.extractUserInput(input).length,
    });
  }

  /**
   * Update Langfuse span with success
   */
  static updateSpanWithSuccess(
    context: AgentExecutionContext,
    result?: AgentResult,
    workflowType: string = 'unified-rag'
  ): void {
    if (context.agentSpan) {
      try {
        const updateData: any = {
          metadata: {
            executionTime: Date.now() - context.startTime,
            workflowType,
            success: true,
          },
        };

        if (result?.output?.response) {
          updateData.output = result.output.response.substring(0, 1000);
        }

        context.agentSpan.update(updateData);
        context.agentSpan.end();
      } catch (error) {
        context.logger.warn('[AskAIAgent] Failed to update Langfuse span');
      }
    }
  }

  /**
   * Update Langfuse span with error
   */
  static updateSpanWithError(
    context: AgentExecutionContext,
    error: Error
  ): void {
    if (context.agentSpan) {
      try {
        context.agentSpan.update({
          metadata: {
            error: error.message,
            executionTime: Date.now() - context.startTime,
            success: false,
          },
        });
        context.agentSpan.end();
      } catch (spanError) {
        context.logger.warn('[AskAIAgent] Failed to update Langfuse span with error');
      }
    }
  }

  /**
   * Handle execution error
   */
  static handleExecutionError(
    context: AgentExecutionContext,
    error: Error,
    operation: string
  ): { elapsedMs: number; errorResult: AgentResult } {
    const elapsedMs = Date.now() - context.startTime;
    
    context.logger.error(error, `AskAIAgent ${operation} failed`, {
      sessionId: context.sessionId,
      elapsedMs,
    });

    // Update span with error
    this.updateSpanWithError(context, error);

    return {
      elapsedMs,
      errorResult: ResponseBuildingUtils.createErrorResult(error, context.sessionId, elapsedMs),
    };
  }

  /**
   * Handle streaming execution error
   */
  static handleStreamingError(
    context: AgentExecutionContext,
    error: Error,
    operation: string
  ): { elapsedMs: number; errorChunk: AgentChunk } {
    const elapsedMs = Date.now() - context.startTime;
    
    context.logger.error(error, `AskAIAgent ${operation} failed`, {
      sessionId: context.sessionId,
      elapsedMs,
    });

    // Update span with error
    this.updateSpanWithError(context, error);

    return {
      elapsedMs,
      errorChunk: ResponseBuildingUtils.createErrorChunk(error, context.sessionId, elapsedMs),
    };
  }
}
