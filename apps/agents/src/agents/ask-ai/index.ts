import { AbstractAgent } from '../../base/agent';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  AgentR<PERSON>ult,
  AgentConfig,
  AgentArchetype,
  AgentCapability,
  AgentMemory,
  AgentChunk,
} from '../../base/types';
import { MCPConnectionManager } from '../../integration/mcp-server/connection-manager';
// import { LangGraphIntegration } from '../../integration/langgraph/langgraph-integration';
import { InMemoryAgentMemory, RedisBackedMemory } from '../../base/memory';

// Import the unified RAG implementation
import {
  UnifiedRAGExecutor,
} from './core/unified-rag';

// Import utility classes
import { RAGExecutionUtils } from './core/unified-rag/utils/rag-execution-utils';
import { ResponseBuildingUtils } from './core/unified-rag/utils/response-building-utils';
import { MemoryUtils } from './core/unified-rag/utils/memory-utils';
import { AgentExecutionUtils } from './core/unified-rag/utils/agent-execution-utils';
import { StreamingResponseUtils } from './core/unified-rag/utils/streaming-response-utils';

// Langfuse observability
import { getLogger } from '../../observability/global-logger';

/**
 * AskAIAgent - The main AskAI agent using the unified RAG implementation
 * that consolidates streaming and non-streaming logic into a single, maintainable system.
 *
 * This version uses the unified RAG architecture that eliminates code duplication
 * while preserving distinct response patterns for streaming and non-streaming modes.
 */
export class AskAIAgent extends AbstractAgent {
  private connectionManager: MCPConnectionManager;
  private memory: AgentMemory;
  private responseMode: 'single-shot' | 'streaming';

  /** LangGraph integration for advanced workflow orchestration */
  // TODO: Permanently remove this here. We will setup another agent and endpoint for this.
  // private langGraphIntegration: LangGraphIntegration;

  /** Unified RAG executor for shared core logic */
  private unifiedRAGExecutor: UnifiedRAGExecutor;

  constructor(
    connectionManager: MCPConnectionManager,
    responseMode: 'single-shot' | 'streaming' = 'single-shot',
    memory?: AgentMemory
  ) {
    const config: AgentConfig = {
      name: 'ask_ai',
      version: '0.0.1',
      description:
        'AskAI agent using unified RAG implementation that consolidates streaming and non-streaming logic.',
      archetype: AgentArchetype.HYBRID,
      capabilities: [
        AgentCapability.TOOL_USE,
        AgentCapability.LLM_INTEGRATION,
        AgentCapability.DATABASE_ACCESS,
        AgentCapability.MEMORY,
        AgentCapability.STREAMING,
      ],
    };
    super(config);

    this.connectionManager = connectionManager;
    this.responseMode = responseMode;

    // Use Redis-backed memory if Redis is available, otherwise fallback to in-memory
    if (connectionManager.getRedisClient()) {
      this.memory =
        memory ||
        new RedisBackedMemory(
          100, // maxEntries
          connectionManager.getRedisClient(),
          {
            redisKeyPrefix: 'conversation_memory:',
            redisTtlSeconds: 86400, // 24 hours
            logger: getLogger(),
          }
        );
    } else {
      this.memory = memory || new InMemoryAgentMemory(100);
    }

    // Initialize unified RAG executor
    this.unifiedRAGExecutor = new UnifiedRAGExecutor(
      this.memory,
      getLogger(),
      this.connectionManager.getRedisClient()
    );
  }

  /**
   * Initialize the agent
   */
  protected async onInitialize(): Promise<void> {
    try {
      // Initialize LangGraph supervisor
      // TODO: Permanently remove this here. We will setup another agent and endpoint for this.
      // this.langGraphIntegration.initializeSupervisor(this.connectionManager);

      getLogger().info('AskAIAgent initialized successfully', {
        agentName: this.config.name,
        version: this.config.version,
        responseMode: this.responseMode,
      });
    } catch (error) {
      getLogger().error(error, 'Failed to initialize AskAIAgent', {
        agentName: this.config.name,
      });
      throw error;
    }
  }

  /**
   * Clean up resources
   */
  protected async onDestroy(): Promise<void> {
    try {
      await this.connectionManager.shutdown();
      getLogger().info('AskAIAgent destroyed successfully', {
        agentName: this.config.name,
      });
    } catch (error) {
      getLogger().error(error, 'Error during AskAIAgent destruction', {
        agentName: this.config.name,
      });
    }
  }

  /**
   * Handle single-shot requests
   */
  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    // Initialize execution context
    const context = AgentExecutionUtils.initializeExecutionContext(
      input,
      this.config.name,
      this.config.version,
      this.responseMode
    );

    // Log execution start
    AgentExecutionUtils.logExecutionStart(context, this.responseMode, input);

    // Validate input
    AgentExecutionUtils.validateInput(input);

    try {
      // Execute unified RAG
      const { ragContext, responseResult } = await RAGExecutionUtils.executeUnifiedRAG(
        input,
        context.sessionId,
        context.traceId,
        context.logger,
        this.unifiedRAGExecutor,
        this.responseMode
      );

      // Store assistant response
      await MemoryUtils.storeAssistantResponse(
        ragContext,
        responseResult,
        context.logger,
        this.memory,
        this.responseMode
      );

      // Build result
      const totalTime = Date.now() - context.startTime;
      const result = ResponseBuildingUtils.buildAgentResult(
        responseResult,
        ragContext,
        context.sessionId,
        totalTime
      );

      // Update span with success
      AgentExecutionUtils.updateSpanWithSuccess(context, result);

      return result;
    } catch (error) {
      const { errorResult } = AgentExecutionUtils.handleExecutionError(
        context,
        error as Error,
        'doInvoke'
      );
      return errorResult;
    }
  }

  /**
   * Handle streaming requests
   */
  protected async *doStream(input: AgentInput): AsyncGenerator<AgentChunk> {
    // Initialize execution context
    const context = AgentExecutionUtils.initializeExecutionContext(
      input,
      this.config.name,
      this.config.version,
      this.responseMode
    );

    // Log execution start
    AgentExecutionUtils.logExecutionStart(context, this.responseMode, input);

    // Validate input
    AgentExecutionUtils.validateInput(input);

    try {
      // Execute unified RAG for streaming
      const { ragContext, responseResult } = await RAGExecutionUtils.executeStreamingRAG(
        input,
        context.sessionId,
        context.traceId,
        context.logger,
        this.unifiedRAGExecutor
      );

      // Process streaming response
      yield* StreamingResponseUtils.processStreamingResponse(
        responseResult,
        ragContext,
        context,
        this.responseMode,
        this.memory,
        context.logger
      );

      // Update span with success
      AgentExecutionUtils.updateSpanWithSuccess(context);
    } catch (error) {
      const { errorChunk } = AgentExecutionUtils.handleStreamingError(
        context,
        error as Error,
        'doStream'
      );
      yield errorChunk;
    }
  }
}
