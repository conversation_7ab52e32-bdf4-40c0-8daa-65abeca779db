import { test } from 'tap';
import { build } from '../../../../../../apps/api/src/app';
import { FastifyInstance } from 'fastify';

/**
 * Test suite for refactored embeddings routes
 * Verifies that the service layer refactoring maintains API compatibility
 */

test('Refactored Embeddings Routes', async (t) => {
  let app: FastifyInstance;

  t.beforeEach(async () => {
    app = build({ logger: false });
    await app.ready();
  });

  t.afterEach(async () => {
    await app.close();
  });

  t.test('GET /api/v1/external/embeddings - should require organization header', async (t) => {
    const response = await app.inject({
      method: 'GET',
      url: '/api/v1/external/embeddings',
      headers: {
        'authorization': 'Bearer test-token',
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'MISSING_ORGANIZATION_ID');
    t.equal(body.message, 'X-Organization-Id header is required and must be a string');
  });

  t.test('POST /api/v1/external/embeddings/update - should require organization header', async (t) => {
    const response = await app.inject({
      method: 'POST',
      url: '/api/v1/external/embeddings/update',
      headers: {
        'authorization': 'Bearer test-token',
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'MISSING_ORGANIZATION_ID');
    t.equal(body.message, 'X-Organization-Id header is required and must be a string');
  });

  t.test('POST /api/v1/external/embeddings/submit - should require organization header', async (t) => {
    const response = await app.inject({
      method: 'POST',
      url: '/api/v1/external/embeddings/submit',
      headers: {
        'authorization': 'Bearer test-token',
      },
      payload: {
        documents: [
          {
            id: 'test-doc-1',
            name: 'Test Document',
            content: 'This is a test document with sufficient content for processing.',
          },
        ],
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'MISSING_ORGANIZATION_ID');
    t.equal(body.message, 'X-Organization-Id header is required and must be a string');
  });

  t.test('POST /api/v1/external/embeddings/submit - should validate request body', async (t) => {
    const response = await app.inject({
      method: 'POST',
      url: '/api/v1/external/embeddings/submit',
      headers: {
        'authorization': 'Bearer test-token',
        'x-organization-id': 'test-org-123',
      },
      payload: {
        // Missing documents array
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'INVALID_REQUEST_BODY');
    t.equal(body.message, 'Request body must contain a documents array');
  });

  t.test('POST /api/v1/external/embeddings/submit - should validate document structure', async (t) => {
    const response = await app.inject({
      method: 'POST',
      url: '/api/v1/external/embeddings/submit',
      headers: {
        'authorization': 'Bearer test-token',
        'x-organization-id': 'test-org-123',
      },
      payload: {
        documents: [
          {
            id: 'test-doc-1',
            // Missing name and content
          },
        ],
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'INVALID_DOCUMENT');
    t.equal(body.message, 'Each document must have id, name, and content fields');
  });

  t.test('POST /api/v1/external/embeddings/submit - should validate content length', async (t) => {
    const response = await app.inject({
      method: 'POST',
      url: '/api/v1/external/embeddings/submit',
      headers: {
        'authorization': 'Bearer test-token',
        'x-organization-id': 'test-org-123',
      },
      payload: {
        documents: [
          {
            id: 'test-doc-1',
            name: 'Test Document',
            content: 'Short', // Less than 20 characters
          },
        ],
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'INSUFFICIENT_CONTENT');
    t.match(body.message, /Document test-doc-1 has insufficient content/);
  });

  t.test('Service layer error handling - should handle service errors gracefully', async (t) => {
    // This test verifies that service layer errors are properly caught and transformed
    // into appropriate HTTP responses by the route handlers
    
    const response = await app.inject({
      method: 'GET',
      url: '/api/v1/external/embeddings',
      headers: {
        'authorization': 'Bearer test-token',
        'x-organization-id': 'invalid-org-format', // This should trigger validation error
      },
    });

    // The service should handle this gracefully and return appropriate error
    t.ok(response.statusCode >= 400);
    const body = JSON.parse(response.body);
    t.ok(body.error);
    t.ok(body.code);
    t.ok(body.message);
  });

  t.end();
});
